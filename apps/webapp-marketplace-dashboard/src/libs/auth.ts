import type { User as DbUser } from '@package/db/core/models/user.model';
import NextAuth from 'next-auth';
import { authConfig } from './auth.config';

// Declare module augmentation for next-auth types
declare module 'next-auth' {
  interface Session {
    user: DbUser & {
      id: string;
      role?: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
    };
  }

  // Also extend the JWT user representation
  interface JWT {
    user: DbUser & {
      id: string;
      role?: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
    };
  }
}

export const { auth, signIn, signOut, handlers } = NextAuth({
  ...authConfig,
  logger: {
    debug: (...messages) => console.debug('DEBUG', ...messages),
  },
  // Type-safe callbacks
  callbacks: {
    ...authConfig.callbacks,
  },
});
