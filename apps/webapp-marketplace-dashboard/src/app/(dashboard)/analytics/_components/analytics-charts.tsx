'use client';

import {
  Chart<PERSON>ontainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import { Area, AreaChart, XAxis, YAxis } from 'recharts';

interface AnalyticsChartsProps {
  type: 'listings' | 'users';
}

export function AnalyticsCharts({ type }: AnalyticsChartsProps) {
  // Mock data for now to avoid server action issues
  const generateMockData = () => {
    const data = [];
    const today = new Date();

    for (let i = 29; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);

      const baseCount = type === 'listings' ? 5 : 2;
      const variation = Math.random() * 0.4 - 0.2;
      const count = Math.max(0, Math.floor(baseCount * (1 + variation)));

      data.push({
        date: date.toISOString().split('T')[0],
        count,
        label: date.toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
        }),
      });
    }

    return data;
  };

  const data = generateMockData();

  const chartConfig = {
    count: {
      label: type === 'listings' ? 'Listings' : 'Users',
      color: 'hsl(var(--primary))',
    },
  };

  return (
    <ChartContainer config={chartConfig} className="h-[300px]">
      <AreaChart data={data}>
        <XAxis
          dataKey="label"
          tickLine={false}
          axisLine={false}
          tickMargin={8}
        />
        <YAxis tickLine={false} axisLine={false} tickMargin={8} />
        <ChartTooltip content={<ChartTooltipContent />} />
        <Area
          dataKey="count"
          type="monotone"
          fill="hsl(var(--primary))"
          fillOpacity={0.4}
          stroke="hsl(var(--primary))"
          strokeWidth={2}
        />
      </AreaChart>
    </ChartContainer>
  );
}
