'use client';

import {
  Chart<PERSON>ontainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import { useEffect, useState } from 'react';
import { Area, AreaChart, XAxis, YAxis } from 'recharts';

import {
  getListingsAnalytics,
  getUsersAnalytics,
} from '../../_actions/analytics.action';

interface AnalyticsChartsProps {
  type: 'listings' | 'users';
}

interface ChartData {
  date: string;
  count: number;
  label: string;
}

export function AnalyticsCharts({ type }: AnalyticsChartsProps) {
  const [data, setData] = useState<ChartData[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const result =
          type === 'listings'
            ? await getListingsAnalytics(30)
            : await getUsersAnalytics(30);

        setData(result.dailyData);
      } catch (error) {
        console.error('Error fetching analytics data:', error);
        setData([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [type]);

  if (loading) {
    return (
      <div className="h-[300px] w-full animate-pulse bg-muted rounded-md" />
    );
  }

  const chartConfig = {
    count: {
      label: type === 'listings' ? 'Listings' : 'Users',
      color: 'hsl(var(--primary))',
    },
  };

  return (
    <ChartContainer config={chartConfig} className="h-[300px]">
      <AreaChart data={data}>
        <defs>
          <linearGradient id="fillCount" x1="0" y1="0" x2="0" y2="1">
            <stop
              offset="5%"
              stopColor="var(--color-count)"
              stopOpacity={0.8}
            />
            <stop
              offset="95%"
              stopColor="var(--color-count)"
              stopOpacity={0.1}
            />
          </linearGradient>
        </defs>
        <XAxis
          dataKey="label"
          tickLine={false}
          axisLine={false}
          tickMargin={8}
          tickFormatter={(value) => value}
        />
        <YAxis
          tickLine={false}
          axisLine={false}
          tickMargin={8}
          tickFormatter={(value) => `${value}`}
        />
        <ChartTooltip
          cursor={false}
          content={<ChartTooltipContent indicator="dot" />}
        />
        <Area
          dataKey="count"
          type="natural"
          fill="url(#fillCount)"
          fillOpacity={0.4}
          stroke="var(--color-count)"
          strokeWidth={2}
        />
      </AreaChart>
    </ChartContainer>
  );
}
