import { redirect } from 'next/navigation';
import { auth } from '../../libs/auth';
import { DashboardLayout } from '../_components/dashboard-layout';

interface LayoutProps {
  children: React.ReactNode;
}

export default async function Layout({ children }: LayoutProps) {
  const session = await auth();
  
  if (!session) {
    redirect('/signin');
  }

  return <DashboardLayout>{children}</DashboardLayout>;
}
