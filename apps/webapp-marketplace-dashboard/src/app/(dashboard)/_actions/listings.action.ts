'use server';

import { backendConfig } from '@package/configs';
import { getListingService } from '@package/db';
import { getDynamoDBClient } from '@package/db/adapters/dynamodb/client';
import type { Listing, ListingStatus, ListingType, ListingCondition } from '@package/db/core/models';

export interface ListingsFilter {
  search?: string;
  status?: ListingStatus;
  type?: ListingType;
  condition?: ListingCondition;
  page?: number;
  limit?: number;
}

export interface ListingsResponse {
  listings: Listing[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

export async function getListings(filter: ListingsFilter = {}): Promise<ListingsResponse> {
  try {
    const config = backendConfig.aws;
    const service = getListingService(
      'dynamodb',
      config.dynamodb.tables.core,
      getDynamoDBClient()
    );

    const { page = 1, limit = 20, search, status, type, condition } = filter;
    
    // For now, we'll use the basic listListings method
    // In a real implementation, you'd want to add proper filtering and pagination
    const allListings = await service.listListings();
    
    // Apply client-side filtering (in production, this should be done at the database level)
    let filteredListings = allListings;
    
    if (search) {
      filteredListings = filteredListings.filter(listing => 
        listing.name.toLowerCase().includes(search.toLowerCase()) ||
        listing.description.toLowerCase().includes(search.toLowerCase())
      );
    }
    
    if (status) {
      filteredListings = filteredListings.filter(listing => listing.status === status);
    }
    
    if (type) {
      filteredListings = filteredListings.filter(listing => listing.type === type);
    }
    
    if (condition) {
      filteredListings = filteredListings.filter(listing => listing.condition === condition);
    }

    // Sort by creation date (newest first)
    filteredListings.sort((a, b) => {
      const dateA = new Date(a.createdAt || 0).getTime();
      const dateB = new Date(b.createdAt || 0).getTime();
      return dateB - dateA;
    });

    // Apply pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedListings = filteredListings.slice(startIndex, endIndex);
    
    return {
      listings: paginatedListings,
      total: filteredListings.length,
      page,
      limit,
      hasMore: endIndex < filteredListings.length,
    };
  } catch (error) {
    console.error('Error fetching listings:', error);
    throw new Error('Failed to fetch listings');
  }
}

export async function getListingById(listingId: string): Promise<Listing | null> {
  try {
    const config = backendConfig.aws;
    const service = getListingService(
      'dynamodb',
      config.dynamodb.tables.core,
      getDynamoDBClient()
    );

    const listing = await service.getListingById(listingId);
    return listing;
  } catch (error) {
    console.error('Error fetching listing:', error);
    return null;
  }
}

export async function getListingsStats() {
  try {
    const config = backendConfig.aws;
    const service = getListingService(
      'dynamodb',
      config.dynamodb.tables.core,
      getDynamoDBClient()
    );

    const allListings = await service.listListings();
    
    const now = new Date();
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    
    const stats = {
      total: allListings.length,
      active: allListings.filter(l => l.status === 'ACTIVE').length,
      thisMonth: allListings.filter(l => {
        const createdAt = new Date(l.createdAt || 0);
        return createdAt >= thisMonth;
      }).length,
      lastMonth: allListings.filter(l => {
        const createdAt = new Date(l.createdAt || 0);
        return createdAt >= lastMonth && createdAt < thisMonth;
      }).length,
    };

    return {
      ...stats,
      growth: stats.lastMonth > 0 ? ((stats.thisMonth - stats.lastMonth) / stats.lastMonth * 100) : 0,
    };
  } catch (error) {
    console.error('Error fetching listings stats:', error);
    throw new Error('Failed to fetch listings stats');
  }
}
