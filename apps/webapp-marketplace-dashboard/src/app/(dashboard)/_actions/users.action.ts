'use server';

import { getUserService } from '@package/db';
import { getDynamoDBClient } from '@package/db/adapters/dynamodb/client';
import type { User } from '@package/db/core/models';

export interface UsersFilter {
  search?: string;
  page?: number;
  limit?: number;
}

export interface UsersResponse {
  users: User[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

export async function getUsers(filter: UsersFilter = {}): Promise<UsersResponse> {
  try {
    const client = getDynamoDBClient();
    const userService = getUserService('dynamodb', client);

    const { page = 1, limit = 20, search } = filter;
    
    // Note: This is a simplified implementation
    // In a real application, you'd want to implement proper pagination at the database level
    // For now, we'll simulate this by getting all users and filtering/paginating client-side
    
    // Since there's no listUsers method in the current service, we'll need to implement this
    // For now, let's return a mock response
    const mockUsers: User[] = [
      {
        id: '1',
        name: '<PERSON>',
        email: '<EMAIL>',
        password: 'hashed',
        role: 'USER',
        createdAt: new Date('2024-01-15'),
        updatedAt: new Date('2024-01-15'),
        emailVerified: new Date('2024-01-15'),
      },
      {
        id: '2',
        name: 'Jane Smith',
        email: '<EMAIL>',
        password: 'hashed',
        role: 'USER',
        createdAt: new Date('2024-02-10'),
        updatedAt: new Date('2024-02-10'),
        emailVerified: new Date('2024-02-10'),
      },
      {
        id: '3',
        name: 'Admin User',
        email: '<EMAIL>',
        password: 'hashed',
        role: 'ADMIN',
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
        emailVerified: new Date('2024-01-01'),
      },
    ];

    let filteredUsers = mockUsers;
    
    if (search) {
      filteredUsers = filteredUsers.filter(user => 
        user.name?.toLowerCase().includes(search.toLowerCase()) ||
        user.email.toLowerCase().includes(search.toLowerCase())
      );
    }

    // Sort by creation date (newest first)
    filteredUsers.sort((a, b) => {
      const dateA = new Date(a.createdAt).getTime();
      const dateB = new Date(b.createdAt).getTime();
      return dateB - dateA;
    });

    // Apply pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex);
    
    return {
      users: paginatedUsers,
      total: filteredUsers.length,
      page,
      limit,
      hasMore: endIndex < filteredUsers.length,
    };
  } catch (error) {
    console.error('Error fetching users:', error);
    throw new Error('Failed to fetch users');
  }
}

export async function getUserById(userId: string): Promise<User | null> {
  try {
    const client = getDynamoDBClient();
    const userService = getUserService('dynamodb', client);

    const user = await userService.getUserById(userId);
    return user;
  } catch (error) {
    console.error('Error fetching user:', error);
    return null;
  }
}

export async function getUsersStats() {
  try {
    // Mock implementation - in a real app, you'd query the database
    const mockUsers = [
      { createdAt: new Date('2024-01-15') },
      { createdAt: new Date('2024-02-10') },
      { createdAt: new Date('2024-03-05') },
      { createdAt: new Date('2024-03-20') },
      { createdAt: new Date('2024-04-01') },
    ];
    
    const now = new Date();
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    
    const stats = {
      total: mockUsers.length,
      thisMonth: mockUsers.filter(u => {
        const createdAt = new Date(u.createdAt);
        return createdAt >= thisMonth;
      }).length,
      lastMonth: mockUsers.filter(u => {
        const createdAt = new Date(u.createdAt);
        return createdAt >= lastMonth && createdAt < thisMonth;
      }).length,
    };

    return {
      ...stats,
      growth: stats.lastMonth > 0 ? ((stats.thisMonth - stats.lastMonth) / stats.lastMonth * 100) : 0,
    };
  } catch (error) {
    console.error('Error fetching users stats:', error);
    throw new Error('Failed to fetch users stats');
  }
}
