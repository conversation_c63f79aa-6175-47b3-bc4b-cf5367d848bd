'use server';

import { getListingsStats } from './listings.action';
import { getUsersStats } from './users.action';

export interface AnalyticsData {
  listings: {
    total: number;
    active: number;
    thisMonth: number;
    growth: number;
    dailyData: Array<{ date: string; count: number }>;
  };
  users: {
    total: number;
    thisMonth: number;
    growth: number;
    dailyData: Array<{ date: string; count: number }>;
  };
  overview: {
    totalListings: number;
    totalUsers: number;
    activeListings: number;
    newListingsThisMonth: number;
    newUsersThisMonth: number;
  };
}

export async function getAnalyticsData(): Promise<AnalyticsData> {
  try {
    const [listingsStats, usersStats] = await Promise.all([
      getListingsStats(),
      getUsersStats(),
    ]);

    // Generate mock daily data for the last 30 days
    const generateDailyData = (baseCount: number) => {
      const data = [];
      const today = new Date();
      
      for (let i = 29; i >= 0; i--) {
        const date = new Date(today);
        date.setDate(date.getDate() - i);
        
        // Generate some realistic-looking data with variation
        const variation = Math.random() * 0.3 - 0.15; // ±15% variation
        const count = Math.max(0, Math.floor(baseCount * (1 + variation)));
        
        data.push({
          date: date.toISOString().split('T')[0],
          count,
        });
      }
      
      return data;
    };

    const listingsDailyData = generateDailyData(listingsStats.thisMonth / 30);
    const usersDailyData = generateDailyData(usersStats.thisMonth / 30);

    return {
      listings: {
        total: listingsStats.total,
        active: listingsStats.active,
        thisMonth: listingsStats.thisMonth,
        growth: listingsStats.growth,
        dailyData: listingsDailyData,
      },
      users: {
        total: usersStats.total,
        thisMonth: usersStats.thisMonth,
        growth: usersStats.growth,
        dailyData: usersDailyData,
      },
      overview: {
        totalListings: listingsStats.total,
        totalUsers: usersStats.total,
        activeListings: listingsStats.active,
        newListingsThisMonth: listingsStats.thisMonth,
        newUsersThisMonth: usersStats.thisMonth,
      },
    };
  } catch (error) {
    console.error('Error fetching analytics data:', error);
    throw new Error('Failed to fetch analytics data');
  }
}

export async function getListingsAnalytics(days: number = 30) {
  try {
    const listingsStats = await getListingsStats();
    
    // Generate daily data for the specified number of days
    const dailyData = [];
    const today = new Date();
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      
      // Mock data - in a real app, you'd query the database for actual daily counts
      const baseCount = listingsStats.thisMonth / 30;
      const variation = Math.random() * 0.4 - 0.2; // ±20% variation
      const count = Math.max(0, Math.floor(baseCount * (1 + variation)));
      
      dailyData.push({
        date: date.toISOString().split('T')[0],
        count,
        label: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      });
    }

    return {
      ...listingsStats,
      dailyData,
    };
  } catch (error) {
    console.error('Error fetching listings analytics:', error);
    throw new Error('Failed to fetch listings analytics');
  }
}

export async function getUsersAnalytics(days: number = 30) {
  try {
    const usersStats = await getUsersStats();
    
    // Generate daily data for the specified number of days
    const dailyData = [];
    const today = new Date();
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      
      // Mock data - in a real app, you'd query the database for actual daily counts
      const baseCount = usersStats.thisMonth / 30;
      const variation = Math.random() * 0.3 - 0.15; // ±15% variation
      const count = Math.max(0, Math.floor(baseCount * (1 + variation)));
      
      dailyData.push({
        date: date.toISOString().split('T')[0],
        count,
        label: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      });
    }

    return {
      ...usersStats,
      dailyData,
    };
  } catch (error) {
    console.error('Error fetching users analytics:', error);
    throw new Error('Failed to fetch users analytics');
  }
}
