import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Edit, Eye, Trash2 } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

import {
  getListings,
  type ListingsFilter,
} from '../../_actions/listings.action';
import { ListingsPagination } from './listings-pagination';

interface ListingsTableProps {
  searchParams: {
    search?: string;
    status?: string;
    type?: string;
    condition?: string;
    page?: string;
    limit?: string;
  };
}

export async function ListingsTable({ searchParams }: ListingsTableProps) {
  const filter: ListingsFilter = {
    search: searchParams.search,
    status: searchParams.status as any,
    type: searchParams.type as any,
    condition: searchParams.condition as any,
    page: parseInt(searchParams.page || '1'),
    limit: parseInt(searchParams.limit || '20'),
  };

  const { listings, total, page, limit, hasMore } = await getListings(filter);

  const getStatusBadge = (status: string) => {
    const variants = {
      ACTIVE: 'default',
      INACTIVE: 'secondary',
      PENDING: 'outline',
      SOLD: 'destructive',
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>
        {status}
      </Badge>
    );
  };

  const getTypeBadge = (type: string) => {
    const variants = {
      SELL: 'default',
      RENT: 'secondary',
      FREE: 'outline',
    } as const;

    return (
      <Badge variant={variants[type as keyof typeof variants] || 'secondary'}>
        {type}
      </Badge>
    );
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <p className="text-sm text-muted-foreground">
          Showing {listings.length} of {total} listings
        </p>
      </div>

      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[100px]">Image</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Condition</TableHead>
              <TableHead>Price</TableHead>
              <TableHead>Created</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {listings.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="h-24 text-center">
                  No listings found.
                </TableCell>
              </TableRow>
            ) : (
              listings.map((listing) => (
                <TableRow key={listing.listingId}>
                  <TableCell>
                    <div className="relative w-16 h-16 overflow-hidden rounded-md">
                      {listing.images && listing.images.length > 0 ? (
                        <Image
                          src={listing.images[0].url}
                          alt={listing.name}
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <div className="flex items-center justify-center w-full h-full bg-muted">
                          <span className="text-xs text-muted-foreground">
                            No image
                          </span>
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <p className="font-medium">{listing.name}</p>
                      <p className="text-sm text-muted-foreground line-clamp-1">
                        {listing.description}
                      </p>
                    </div>
                  </TableCell>
                  <TableCell>{getTypeBadge(listing.type)}</TableCell>
                  <TableCell>{getStatusBadge(listing.status)}</TableCell>
                  <TableCell>
                    <Badge variant="outline">{listing.condition}</Badge>
                  </TableCell>
                  <TableCell>
                    <span className="font-medium">
                      ${listing.price.toFixed(2)}
                      {listing.rentalUnit && (
                        <span className="text-sm text-muted-foreground">
                          /{listing.rentalUnit.toLowerCase()}
                        </span>
                      )}
                    </span>
                  </TableCell>
                  <TableCell>
                    {listing.createdAt
                      ? new Date(listing.createdAt).toLocaleDateString()
                      : 'N/A'}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end gap-2">
                      <Button variant="ghost" size="sm" asChild>
                        <Link href={`/dashboard/listings/${listing.listingId}`}>
                          <Eye className="w-4 h-4" />
                        </Link>
                      </Button>
                      <Button variant="ghost" size="sm" asChild>
                        <Link
                          href={`/dashboard/listings/${listing.listingId}/edit`}
                        >
                          <Edit className="w-4 h-4" />
                        </Link>
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      <ListingsPagination
        currentPage={page}
        totalItems={total}
        itemsPerPage={limit}
        hasMore={hasMore}
      />
    </div>
  );
}
