import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>eader,
  CardTitle,
} from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Suspense } from 'react';
import { ListingsFilters } from './_components/listings-filters';
import { ListingsTable } from './_components/listings-table';

interface ListingsPageProps {
  searchParams: Promise<{
    search?: string;
    status?: string;
    type?: string;
    condition?: string;
    page?: string;
    limit?: string;
  }>;
}

export default async function ListingsPage({
  searchParams,
}: ListingsPageProps) {
  const resolvedSearchParams = await searchParams;
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">
          Listings Management
        </h1>
        <p className="text-muted-foreground">Manage all marketplace listings</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
          <CardDescription>Filter and search through listings</CardDescription>
        </CardHeader>
        <CardContent>
          <ListingsFilters />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>All Listings</CardTitle>
          <CardDescription>
            View and manage marketplace listings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<ListingsTableSkeleton />}>
            <ListingsTable searchParams={resolvedSearchParams} />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
}

function ListingsTableSkeleton() {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Skeleton className="h-8 w-[200px]" />
        <Skeleton className="h-8 w-[100px]" />
      </div>
      <div className="space-y-2">
        {Array.from({ length: 5 }).map((_, i) => (
          <Skeleton key={i} className="w-full h-16" />
        ))}
      </div>
    </div>
  );
}
