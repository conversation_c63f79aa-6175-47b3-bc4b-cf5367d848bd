'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { useState } from 'react';
import { Search, X } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

export function UsersFilters() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [search, setSearch] = useState(searchParams.get('search') || '');

  const updateFilters = () => {
    const params = new URLSearchParams();
    
    if (search) params.set('search', search);
    
    // Reset to first page when filters change
    params.set('page', '1');
    
    router.push(`/dashboard/users?${params.toString()}`);
  };

  const clearFilters = () => {
    setSearch('');
    router.push('/dashboard/users');
  };

  const hasActiveFilters = search;

  return (
    <div className="space-y-4">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="space-y-2">
          <Label htmlFor="search">Search</Label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              id="search"
              placeholder="Search users by name or email..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="pl-9"
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  updateFilters();
                }
              }}
            />
          </div>
        </div>
      </div>

      <div className="flex items-center gap-2">
        <Button onClick={updateFilters}>
          Apply Filters
        </Button>
        
        {hasActiveFilters && (
          <Button variant="outline" onClick={clearFilters}>
            <X className="mr-2 h-4 w-4" />
            Clear Filters
          </Button>
        )}
      </div>
    </div>
  );
}
