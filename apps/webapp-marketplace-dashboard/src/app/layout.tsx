import './global.css';

import { Providers } from './_components/providers';

export const metadata = {
  title: 'Welcome to CM Dashboard',
  description: 'Circular Marketplace Dashboard',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className="flex flex-col w-full min-h-screen">
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}
