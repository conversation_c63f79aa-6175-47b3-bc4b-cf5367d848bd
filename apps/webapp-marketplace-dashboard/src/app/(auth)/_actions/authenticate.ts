'use server';

import { AuthError } from 'next-auth';
import { signIn } from '../../../libs/auth';

export async function authenticate(
  provider: string,
  formData: FormData,
  callbackUrl = '/'
) {
  try {
    // Extract credentials from formData
    const credentials = {
      email: formData.get('email'),
      password: formData.get('password'),
      callbackUrl,
    };

    // Call NextAuth's signIn with explicit method
    await signIn(provider, {
      ...credentials,
      redirect: false,
    });

    // If we got here, authentication was successful
    return null;
  } catch (error) {
    // Handle authentication errors
    if (error instanceof AuthError) {
      switch (error.type) {
        case 'CredentialsSignin':
          return 'Invalid credentials';
        default:
          return 'Something went wrong';
      }
    }
    return 'Something went wrong';
  }
}
