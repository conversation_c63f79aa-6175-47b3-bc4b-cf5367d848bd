'use server';

import { getUserService } from '@package/db';
import { getDynamoDBClient } from '@package/db/adapters/dynamodb/client';
import type { User } from '@package/db/core/models/user.model';

import { hashPassword } from '@package/security';

import { authenticate } from '../_actions/authenticate';

export async function signUp(formData: FormData) {
  try {
    const client = getDynamoDBClient();
    const userService = getUserService('dynamodb', client);

    const newUser = {
      id: '000-000',
      email: formData.get('email') as string,
      password: await hashPassword(formData.get('password') as string),
      name: formData.get('name') as string,
      role: 'USER',
      emailVerified: new Date(),
      createdAt: new Date(),
    } as User;

    const isUserExists = await userService.getUserByEmail(newUser.email);
    if (isUserExists) {
      throw new Error('User already exists');
    }

    await userService.createUser(newUser);
    await authenticate('credentials', formData);

    return Promise.resolve();
  } catch (error: unknown) {
    if (error instanceof Error && error.message.includes('CredentialsSignin')) {
      return 'Invalid credentials.';
    }
    return 'An unknown error occurred';
  }
}
