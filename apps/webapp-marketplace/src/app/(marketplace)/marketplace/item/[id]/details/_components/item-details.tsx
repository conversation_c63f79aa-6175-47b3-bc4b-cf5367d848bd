'use client';

import { addDays, format } from 'date-fns';
import {
  AlertCircle,
  Calendar,
  CheckCircle,
  Clock,
  Edit,
  Gift,
  Handshake,
  Loader,
  MessageCircle,
  Package,
  Tag,
  Wallet,
} from 'lucide-react';
import moment from 'moment';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useState, useTransition } from 'react';
import { useForm } from 'react-hook-form';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Separator } from '@/components/ui/separator';

import { cn } from '@/lib/utils';

import {
  BookingStatus,
  type Feedback,
  type Listing,
  ListingCondition,
  ListingRentalUnit,
  ListingStatus,
  ListingType,
  PaymentStatus,
  ReturnStatus,
  type User,
} from '@package/db/core/models';

import { createBooking } from '../../../../_actions/booking.action';
import { createMessage } from '../_actions/messaging.action';

import { ItemDescription } from './item-description';
import InformationSafety from './item-info-safety';
import { ItemInformation } from './item-information';
import ItemRelatedListings from './item-listing-related';
import { ItemMainImage } from './item-main-image';
import InformationSeller from './item-seller';
import { ItemShippingPolicies } from './item-shipping-policies';
import { ItemThumbnails } from './item-thumbnails';

import { edgeConfig } from '@package/configs';

type MainImage = Listing['images'][number];

function formatDate(dateString?: string): string {
  if (!dateString) return 'Recently';
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}

function getConditionLabel(condition: ListingCondition): string {
  switch (condition) {
    case ListingCondition.NEW:
      return 'Brand New';
    case ListingCondition.LIKE_NEW:
      return 'Like New';
    case ListingCondition.GOOD:
      return 'Good';
    case ListingCondition.FAIR:
      return 'Fair';
    case ListingCondition.POOR:
      return 'Poor';
    default:
      return condition;
  }
}

function getConditionColor(condition: ListingCondition): string {
  switch (condition) {
    case ListingCondition.NEW:
      return 'bg-green-100 text-green-800 border-green-200';
    case ListingCondition.LIKE_NEW:
      return 'bg-emerald-100 text-emerald-800 border-emerald-200';
    case ListingCondition.GOOD:
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case ListingCondition.FAIR:
      return 'bg-amber-100 text-amber-800 border-amber-200';
    case ListingCondition.POOR:
      return 'bg-red-100 text-red-800 border-red-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
}

interface BookingFormData {
  paymentMethod: 'cash' | 'bank_transfer';
}

interface ContactFormData {
  message: string;
}

interface ItemDetailsProps {
  listing: Listing;
  userListings: Listing[];
  seller: User;
  relatedListings: Listing[];
  enableContactSeller?: boolean;
  enableAddToCart?: boolean;
  sellerFeedback: Feedback[];
}

// Helper to display rental availability exactly as set in listing-edit.tsx, using 24-hour format and parseZone for local time
function formatRentalDate(dateString: string) {
  // Use moment.parseZone to ensure no timezone shift, matching datetime-local input
  return moment.parseZone(dateString).format('YYYY-MM-DD HH:mm');
}

export default function ItemDetails({
  listing,
  userListings,
  seller,
  relatedListings,
  enableContactSeller,
  enableAddToCart,
  sellerFeedback,
}: ItemDetailsProps) {
  const { data: session } = useSession();
  const router = useRouter();

  const [isCreatingBooking, setStartCreatingBookingTransition] =
    useTransition();

  const [mainImage, setMainImage] = useState<MainImage>(
    listing.images.find((image) => image.isMain) || listing.images[0]
  );
  const [isBookingOpen, setIsBookingOpen] = useState(false);
  const [bookingStartDate, setBookingStartDate] = useState<Date>(
    moment().add(1, 'days').toDate()
  );
  const [bookingEndDate, setBookingEndDate] = useState<Date>(
    moment().add(2, 'days').toDate()
  );
  const [bookingSuccess, setBookingSuccess] = useState<string | null>(null);
  const [bookingError, setBookingError] = useState<string | null>(null);
  const [calculatedPrice, setCalculatedPrice] = useState<number>(listing.price);

  // message state
  const [isContactFormOpen, setIsContactFormOpen] = useState(false);
  const [contactBuyerSuccess, setContactBuyerSuccess] = useState<string | null>(
    null
  );
  const [contacBuyerError, setContacBuyerError] = useState<string | null>(null);
  const [isSubmittingContact, setIsSubmittingContact] = useTransition();

  // FORMS

  const form = useForm<BookingFormData>({
    defaultValues: {
      paymentMethod: 'cash',
    },
  });

  const contactForm = useForm<ContactFormData>({
    defaultValues: {
      message: '',
    },
  });

  // EVENTS

  const handleClickImage = (image: MainImage) => {
    setMainImage(image);
  };

  // Get attribute values by key with default fallbacks
  const getAttributeValue = (key: string, defaultValue = 'Not specified') => {
    if (!listing.attributes) return defaultValue;
    const attr = listing.attributes.find((attr) => attr.key === key);
    return attr ? attr.value : defaultValue;
  };

  const conditionSummary = getAttributeValue('conditionSummary', '');
  const deliveryOptions = getAttributeValue('deliveryOptions', '').split(',');
  const safetyRequirements = getAttributeValue('safetyRequirements', '');

  const calculateRentalPrice = (start: Date, end: Date) => {
    if (!listing.rentalUnit) return listing.price;

    const diffTime = Math.abs(end.getTime() - start.getTime());
    let duration = 1;

    switch (listing.rentalUnit) {
      case ListingRentalUnit.HOUR:
        duration = Math.ceil(diffTime / (1000 * 60 * 60));
        break;
      case ListingRentalUnit.DAY:
        duration = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        break;
      case ListingRentalUnit.WEEK:
        duration = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 7));
        break;
      case ListingRentalUnit.MONTH:
        duration = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 30));
        break;
      default:
        duration = 1;
    }

    return listing.price * duration;
  };

  // Update price calculation when dates change
  const updateCalculatedPrice = (start: Date, end: Date) => {
    setCalculatedPrice(calculateRentalPrice(start, end));
  };

  const handleBookingSubmit = async () => {
    setBookingSuccess(null);
    setBookingError(null);

    setStartCreatingBookingTransition(async () => {
      try {
        const bookingData = {
          listingId: listing.listingId,
          userId: listing.userId,
          renterId: session?.user?.id as string,
          startDate: bookingStartDate.toISOString(),
          endDate: bookingEndDate.toISOString(),
          rentalDuration: Math.ceil(
            Math.abs(bookingEndDate.getTime() - bookingStartDate.getTime()) /
              (1000 * 60 * 60 * 24)
          ),
          status: BookingStatus.PENDING,
          paymentStatus: PaymentStatus.PENDING,
          returnStatus: ReturnStatus.PENDING,
        };

        if (!session) {
          setBookingError(
            'You must be logged in to create a booking. Please log in and try again.'
          );
          return;
        }

        const result = await createBooking(bookingData, listing, seller);

        if (!result) {
          setBookingError(
            'An error occurred while creating your booking. Please try again.'
          );
          return;
        }

        setBookingSuccess(
          'Booking request submitted successfully! The seller will contact you soon.'
        );

        setTimeout(() => {
          setIsBookingOpen(false);
        }, 3000);
      } catch (error) {
        setBookingError(
          'An error occurred while creating your booking. Please try again.'
        );
      }
    });
  };

  // Render the listing action buttons (Book Now, Contact Seller, etc.)
  const actionComponent = () => (
    <>
      {listing.userId === session?.user?.id ? (
        <Button
          className="w-full mb-3"
          onClick={() => {
            router.push(`/marketplace/item/${listing.listingId}/edit`);
            router.refresh();
          }}
        >
          <Edit className="w-4 h-4 mr-2" /> Edit Listing
        </Button>
      ) : (
        <>
          {listing.status !== ListingStatus.ACTIVE && (
            <Button className="w-full mb-3" variant={'outline'}>
              Unavailable
            </Button>
          )}

          {listing.status === ListingStatus.ACTIVE &&
            listing.type === ListingType.RENT && (
              <Button
                className="w-full mb-3"
                onClick={() => {
                  if (
                    listing.rentalAvailability &&
                    listing.rentalAvailability.length > 0
                  ) {
                    const startDate = moment
                      .parseZone(listing.rentalAvailability[0].startDate)
                      .toDate();
                    const endDate = moment
                      .parseZone(listing.rentalAvailability[0].endDate)
                      .toDate();
                    setBookingStartDate(startDate);
                    setBookingEndDate(endDate);
                    updateCalculatedPrice(startDate, endDate);
                  } else {
                    const startDate = addDays(new Date(), 1);
                    const endDate = addDays(new Date(), 2);
                    setBookingStartDate(startDate);
                    setBookingEndDate(endDate);
                    updateCalculatedPrice(startDate, endDate);
                  }
                  setIsBookingOpen(true);
                }}
              >
                {/* FIXME: disable when booking approval is pending */}
                <Calendar className="w-4 h-4 mr-2" /> Book Now
              </Button>
            )}

          {enableContactSeller && (
            <Button
              className="w-full mb-3"
              onClick={() => {
                contactForm.reset();

                setContacBuyerError(null);
                setContactBuyerSuccess(null);
                setIsContactFormOpen(true);
              }}
            >
              <MessageCircle className="w-4 h-4 mr-2" /> Contact Seller
            </Button>
          )}

          {enableAddToCart && (
            <Button variant="outline" className="w-full">
              <Package className="w-4 h-4 mr-2" /> Add to Cart
            </Button>
          )}
        </>
      )}
    </>
  );

  // Renders price information and type badge
  const priceInfoComponent = () => (
    <div className="flex items-center justify-between mb-6">
      <span className="text-3xl font-bold">${listing.price.toFixed(2)}</span>
      <Badge
        className="flex items-center px-3 py-1.5 text-sm"
        variant="secondary"
      >
        {(() => {
          switch (listing.type) {
            case ListingType.SELL:
              return <Tag className="w-4 h-4 mr-2" aria-label="Sell icon" />;
            case ListingType.FREE:
              return <Gift className="w-4 h-4 mr-2" aria-label="Free icon" />;
            case ListingType.RENT:
              return <Clock className="w-4 h-4 mr-2" aria-label="Rent icon" />;
            case ListingType.SWAP:
              return (
                <Handshake className="w-4 h-4 mr-2" aria-label="Swap icon" />
              );
          }
        })()}
        <span>{listing.type}</span>
      </Badge>
    </div>
  );

  const handleContactFormSubmit = (data: ContactFormData) => {
    setContactBuyerSuccess(null);
    setContacBuyerError(null);

    setIsSubmittingContact(async () => {
      try {
        if (data.message.length >= 500) {
          setContacBuyerError('Message cannot exceed 500 characters.');
          return;
        }

        const createMessageResult = createMessage(
          seller,
          listing.listingId,
          listing.name,
          data.message
        );

        if (!createMessageResult) {
          setContacBuyerError(
            'Failed to send message. Please try again later.'
          );
          return;
        }

        setContactBuyerSuccess(
          'Message sent successfully! The seller will respond soon.'
        );
        setTimeout(() => {
          setIsContactFormOpen(false);
        }, 3000);
      } catch (error) {
        console.error('Error sending message:', error);
        setContacBuyerError('Failed to send message. Please try again.');
      }
    });
  };

  const contactFormComponent = () => (
    <Dialog open={isContactFormOpen} onOpenChange={setIsContactFormOpen}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Contact Seller</DialogTitle>
          <DialogDescription>
            Send a message to <b>{seller.name}</b> about this item.
          </DialogDescription>
        </DialogHeader>

        <Form {...contactForm}>
          <form
            onSubmit={contactForm.handleSubmit(handleContactFormSubmit)}
            className="space-y-4"
          >
            <FormField
              control={contactForm.control}
              name="message"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Message</FormLabel>
                  <FormControl>
                    <textarea
                      className="flex min-h-[120px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      placeholder="Type your message here..."
                      minLength={10}
                      maxLength={500}
                      {...field}
                    />
                  </FormControl>
                  {contactForm.formState.errors.message && (
                    <FormMessage>
                      Message must be at least 10 characters
                    </FormMessage>
                  )}
                </FormItem>
              )}
            />

            {(contactBuyerSuccess || contacBuyerError) && (
              <div
                className={`p-4 rounded-md ${
                  contactBuyerSuccess
                    ? 'bg-green-50 border border-green-200'
                    : 'bg-red-50 border border-red-200'
                }`}
              >
                {contactBuyerSuccess && (
                  <div className="flex items-center text-green-800">
                    <CheckCircle className="w-4 h-4 mr-2" />
                    <span>{contactBuyerSuccess}</span>
                  </div>
                )}

                {contacBuyerError && (
                  <div className="flex items-center text-red-800">
                    <AlertCircle className="w-4 h-4 mr-2" />
                    <span>{contacBuyerError}</span>
                  </div>
                )}
              </div>
            )}

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsContactFormOpen(false)}
                disabled={isSubmittingContact}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmittingContact}>
                {isSubmittingContact ? (
                  <>
                    <Loader className="w-4 h-4 mr-2 animate-spin" />
                    Sending...
                  </>
                ) : (
                  'Send Message'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );

  return (
    <div className="space-y-6">
      {/* Mobile-only price and actions card */}
      <Card className="block lg:hidden">
        <CardHeader>
          <div className="flex items-start justify-between">
            <div>
              <CardTitle className="text-xl font-bold">
                {listing.name}
              </CardTitle>
              <CardDescription className="mt-1 text-gray-500">
                <span className="flex items-center gap-1">
                  <Clock className="w-4 h-4" /> Listed{' '}
                  {formatDate(listing.createdAt?.toString())}
                </span>
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {priceInfoComponent()}
          {actionComponent()}
        </CardContent>
      </Card>

      {/* Main content with responsive layout */}
      <div className="grid gap-8 lg:grid-cols-3">
        {/* Left Column: Images & Description */}
        <div className="space-y-8 lg:col-span-2">
          <ItemMainImage
            mainImage={mainImage}
            listing={listing}
            handleClickImage={handleClickImage}
          />
          <ItemThumbnails
            images={listing.images}
            mainImage={mainImage}
            handleClickImage={handleClickImage}
            listing={listing}
          />
          <ItemDescription
            description={listing.description}
            conditionSummary={conditionSummary}
          />
          <ItemInformation
            listing={listing}
            getConditionColor={getConditionColor}
            getConditionLabel={getConditionLabel}
            edgeConfig={edgeConfig}
          />
          <ItemShippingPolicies
            deliveryOptions={deliveryOptions}
            safetyRequirements={safetyRequirements}
          />
          <div className="pt-2">
            <ItemRelatedListings
              listing={listing}
              relatedListings={relatedListings}
            />
          </div>
        </div>

        {/* Right Column: Listing Info & Seller - hidden on mobile */}
        <div className="hidden space-y-6 lg:block">
          {/* Listing Info Card */}
          <Card>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="text-2xl font-bold">
                    {listing.name}
                  </CardTitle>
                  <CardDescription className="mt-1 text-gray-500">
                    <span className="flex items-center gap-1">
                      <Clock className="w-4 h-4" /> Listed{' '}
                      {formatDate(listing.createdAt?.toString())}
                    </span>
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {priceInfoComponent()}
              {actionComponent()}
            </CardContent>
            <CardFooter className="flex flex-col space-y-4">
              {/* Rental Availability - Only show for rental listings */}
              {listing.type === ListingType.RENT && (
                <div className="w-full">
                  <h3 className="mb-2 text-sm font-medium text-gray-500">
                    Availability
                  </h3>
                  {listing.rentalAvailability &&
                  listing.rentalAvailability.length > 0 ? (
                    <div className="p-2 border rounded-md bg-gray-50">
                      <div className="flex items-center text-sm">
                        <Calendar className="w-4 h-4 mr-2 text-gray-500" />
                        <div>
                          <span className="font-medium">
                            Available for rent:
                          </span>{' '}
                          {formatRentalDate(
                            listing.rentalAvailability[0].startDate
                          )}{' '}
                          -{' '}
                          {formatRentalDate(
                            listing.rentalAvailability[0].endDate
                          )}
                        </div>
                      </div>
                      <div className="mt-1 text-xs text-gray-500">
                        <span className="font-medium">Rental unit:</span>{' '}
                        {listing.rentalUnit?.toLowerCase() || 'day'}{' '}
                        {listing.rentalDuration && (
                          <>
                            ({listing.rentalDuration}{' '}
                            {listing.rentalUnit?.toLowerCase() || 'day'}s total)
                          </>
                        )}
                      </div>
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500">
                      No specific availability information provided. Contact
                      seller for details.
                    </p>
                  )}
                </div>
              )}

              {/* Payment Options */}
              <div className="w-full">
                <h3 className="mb-2 text-sm font-medium text-gray-500">
                  Payment Options
                </h3>
                <div className="flex flex-wrap gap-2">
                  <Badge
                    variant="outline"
                    className="flex items-center gap-1 px-2 py-1 text-xs bg-gray-50"
                  >
                    <Wallet className="w-3.5 h-3.5 mr-1" />
                    Cash (Pay onsite)
                  </Badge>
                </div>
              </div>
            </CardFooter>
          </Card>

          {/* Seller Info Card */}
          {seller && (
            <InformationSeller
              seller={seller}
              listings={userListings}
              feedback={sellerFeedback}
            />
          )}

          {/* Safety Notice */}
          <InformationSafety />
        </div>
      </div>

      {/* Mobile-only Seller and Safety info */}
      <div className="block space-y-6 lg:hidden">
        {seller && (
          <InformationSeller
            seller={seller}
            listings={userListings}
            feedback={sellerFeedback}
          />
        )}
        <InformationSafety />
      </div>

      {/* Booking Dialog */}
      <Dialog open={isBookingOpen} onOpenChange={setIsBookingOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Book {listing.name}</DialogTitle>
            <DialogDescription>
              {listing.rentalUnit && (
                <span className="block mt-2">
                  Price: ${listing.price} per {listing.rentalUnit.toLowerCase()}
                </span>
              )}
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(handleBookingSubmit)}
              className="space-y-6"
            >
              {/* Date Selection */}
              <div className="grid gap-4 py-4 sm:grid-cols-2">
                <div className="space-y-2">
                  <FormLabel>Start Date</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          'w-full justify-start text-left font-normal',
                          !bookingStartDate && 'text-muted-foreground'
                        )}
                      >
                        <Calendar className="w-4 h-4 mr-2" />
                        {bookingStartDate
                          ? format(bookingStartDate, 'PPP')
                          : 'Pick a date'}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <CalendarComponent
                        mode="single"
                        selected={bookingStartDate}
                        onSelect={(date) => {
                          if (date) {
                            setBookingStartDate(date);
                            if (date > bookingEndDate) {
                              const newEndDate = addDays(date, 1);
                              setBookingEndDate(newEndDate);
                              updateCalculatedPrice(date, newEndDate);
                            } else {
                              updateCalculatedPrice(date, bookingEndDate);
                            }
                          }
                        }}
                        disabled={(date) =>
                          moment(date).isBefore(moment(), 'minute')
                        }
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="space-y-2">
                  <FormLabel>End Date</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          'w-full justify-start text-left font-normal',
                          !bookingEndDate && 'text-muted-foreground'
                        )}
                      >
                        <Calendar className="w-4 h-4 mr-2" />
                        {bookingEndDate
                          ? format(bookingEndDate, 'PPP')
                          : 'Pick a date'}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <CalendarComponent
                        mode="single"
                        selected={bookingEndDate}
                        onSelect={(date) => {
                          if (date && date > bookingStartDate) {
                            setBookingEndDate(date);
                            updateCalculatedPrice(bookingStartDate, date);
                          }
                        }}
                        disabled={(date) =>
                          moment(date).isSameOrBefore(
                            moment(bookingStartDate),
                            'minute'
                          )
                        }
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>

              {/* Payment Method */}
              <FormField
                control={form.control}
                name="paymentMethod"
                render={({ field }) => (
                  <FormItem className="space-y-3">
                    <FormLabel>Payment Method</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex flex-col space-y-1"
                      >
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="cash" />
                          </FormControl>
                          <FormLabel className="font-normal">
                            Cash (Pay onsite)
                          </FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Price Summary */}
              <div className="p-4 mt-4 border rounded-lg bg-gray-50">
                <div className="flex justify-between">
                  <span>Rental Price:</span>
                  <span>
                    ${listing.price.toFixed(2)} per{' '}
                    {listing.rentalUnit?.toLowerCase()}
                  </span>
                </div>
                <div className="flex justify-between mt-2">
                  <span>Duration:</span>
                  <span>
                    {moment(bookingStartDate).format('MMM D')} -{' '}
                    {moment(bookingEndDate).format('MMM D')}
                  </span>
                </div>
                <Separator className="my-2" />
                <div className="flex justify-between font-semibold">
                  <span>Total:</span>
                  <span>${calculatedPrice.toFixed(2)}</span>
                </div>
              </div>

              {(bookingSuccess || bookingError) && (
                <div
                  className={`p-4 mb-4 rounded-md ${
                    bookingSuccess
                      ? 'bg-green-50 border border-green-200'
                      : 'bg-red-50 border border-red-200'
                  }`}
                >
                  {bookingSuccess && (
                    <div className="flex items-center text-green-800">
                      <CheckCircle className="w-4 h-4 mr-2" />
                      <span>{bookingSuccess}</span>
                    </div>
                  )}

                  {bookingError && (
                    <div className="flex items-center text-red-800">
                      <AlertCircle className="w-4 h-4 mr-2" />
                      <span>{bookingError}</span>
                    </div>
                  )}
                </div>
              )}
              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsBookingOpen(false)}
                  disabled={isCreatingBooking}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isCreatingBooking || !session}>
                  {isCreatingBooking ? (
                    <>
                      <Loader className="w-4 h-4 mr-2 animate-spin" />
                      Booking...
                    </>
                  ) : (
                    'Book Now'
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Contact Seller Form */}
      {contactFormComponent()}
    </div>
  );
}
