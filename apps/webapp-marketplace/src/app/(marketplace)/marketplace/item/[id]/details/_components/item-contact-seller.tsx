'use client';

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, CheckCircle, Loader } from 'lucide-react';
import { useState, useTransition } from 'react';
import { useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';

import { type User } from '@package/db/core/models';

import { createMessage } from '../_actions/messaging.action';

interface ContactFormData {
  message: string;
}

interface ItemContactSellerProps {
  seller: User;
  listingId: string;
  listingName: string;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export function ItemContactSeller({
  seller,
  listingId,
  listingName,
  isOpen,
  onOpenChange,
}: ItemContactSellerProps) {
  const [contactBuyerSuc<PERSON>, setContactBuyerSuccess] = useState<string | null>(
    null
  );
  const [contacBuyerError, setContacBuyerError] = useState<string | null>(null);
  const [isSubmittingContact, setIsSubmittingContact] = useTransition();

  const contactForm = useForm<ContactFormData>({
    defaultValues: {
      message: '',
    },
  });

  const handleContactFormSubmit = (data: ContactFormData) => {
    setContactBuyerSuccess(null);
    setContacBuyerError(null);

    setIsSubmittingContact(async () => {
      try {
        if (data.message.length >= 500) {
          setContacBuyerError('Message cannot exceed 500 characters.');
          return;
        }

        const createMessageResult = createMessage(
          seller,
          listingId,
          listingName,
          data.message
        );

        if (!createMessageResult) {
          setContacBuyerError(
            'Failed to send message. Please try again later.'
          );
          return;
        }

        setContactBuyerSuccess(
          'Message sent successfully! The seller will respond soon.'
        );
        setTimeout(() => {
          onOpenChange(false);
        }, 3000);
      } catch (error) {
        console.error('Error sending message:', error);
        setContacBuyerError('Failed to send message. Please try again.');
      }
    });
  };

  const handleOpenChange = (open: boolean) => {
    if (open) {
      // Reset form and state when opening
      contactForm.reset();
      setContacBuyerError(null);
      setContactBuyerSuccess(null);
    }
    onOpenChange(open);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Contact Seller x</DialogTitle>
          <DialogDescription>
            Send a message to <b>{seller.name}</b> about this item.
          </DialogDescription>
        </DialogHeader>

        <Form {...contactForm}>
          <form
            onSubmit={contactForm.handleSubmit(handleContactFormSubmit)}
            className="space-y-4"
          >
            <FormField
              control={contactForm.control}
              name="message"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Message</FormLabel>
                  <FormControl>
                    <textarea
                      className="flex min-h-[120px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      placeholder="Type your message here..."
                      minLength={10}
                      maxLength={500}
                      {...field}
                    />
                  </FormControl>
                  {contactForm.formState.errors.message && (
                    <FormMessage>
                      Message must be at least 10 characters
                    </FormMessage>
                  )}
                </FormItem>
              )}
            />

            {(contactBuyerSuccess || contacBuyerError) && (
              <div
                className={`p-4 rounded-md ${
                  contactBuyerSuccess
                    ? 'bg-green-50 border border-green-200'
                    : 'bg-red-50 border border-red-200'
                }`}
              >
                {contactBuyerSuccess && (
                  <div className="flex items-center text-green-800">
                    <CheckCircle className="w-4 h-4 mr-2" />
                    <span>{contactBuyerSuccess}</span>
                  </div>
                )}

                {contacBuyerError && (
                  <div className="flex items-center text-red-800">
                    <AlertCircle className="w-4 h-4 mr-2" />
                    <span>{contacBuyerError}</span>
                  </div>
                )}
              </div>
            )}

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isSubmittingContact}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmittingContact}>
                {isSubmittingContact ? (
                  <>
                    <Loader className="w-4 h-4 mr-2 animate-spin" />
                    Sending...
                  </>
                ) : (
                  'Send Message'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
